{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "cypress:open": "cypress open", "cypress:run": "cypress run", "cypress:run:headless": "cypress run --headless", "test:e2e": "cypress run", "test:e2e:open": "cypress open"}, "devDependencies": {"@cypress/webpack-preprocessor": "^6.0.4", "@prettier/plugin-php": "^0.22.4", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.0.0", "@types/lodash": "^4.17.17", "@types/node": "^22.15.21", "@types/react": "^18.3.22", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.5.0", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "concurrently": "^9.0.1", "cypress": "^13.17.0", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^5.4.19"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.4", "@inertiajs/react": "^1.3.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "i18next": "^25.2.0", "laravel-echo": "^1.19.0", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "pusher-js": "^8.4.0", "react": "^18.3.1", "react-day-picker": "^9.7.0", "react-dom": "^18.3.1", "react-i18next": "^15.5.2", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "ziggy-js": "^2.5.3"}, "prettier": {"semi": true, "singleQuote": true, "useTabs": false, "tabWidth": 2, "trailingComma": "all", "printWidth": 80, "arrowParens": "avoid"}}